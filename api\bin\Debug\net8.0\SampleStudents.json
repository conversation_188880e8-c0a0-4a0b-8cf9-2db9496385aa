[{"id": 1, "studentId": 1001, "role": "Student", "fullName": "<PERSON>", "gradeLevel": 5, "section": "A", "academicRecord": {"schoolYear": "2023-2024", "quarters": [{"quarter": 1, "subjects": [{"name": "Mathematics", "score": 88}, {"name": "Science", "score": 92}, {"name": "English", "score": 85}]}, {"quarter": 2, "subjects": [{"name": "Mathematics", "score": 90}, {"name": "Science", "score": 87}, {"name": "English", "score": 88}]}]}}, {"id": 2, "studentId": 1002, "role": "Student", "fullName": "<PERSON>", "gradeLevel": 6, "section": "B", "academicRecord": {"schoolYear": "2023-2024", "quarters": [{"quarter": 1, "subjects": [{"name": "Mathematics", "score": 78}, {"name": "Science", "score": 85}, {"name": "English", "score": 90}]}]}}, {"id": 3, "studentId": 1003, "role": "Student", "fullName": "<PERSON>", "gradeLevel": 7, "section": "C", "academicRecord": {"schoolYear": "2023-2024", "quarters": [{"quarter": 1, "subjects": [{"name": "Mathematics", "score": 92}, {"name": "Science", "score": 88}, {"name": "English", "score": 95}, {"name": "Social Studies", "score": 89}]}]}}, {"id": 4, "studentId": 1004, "role": "Student", "fullName": "<PERSON>", "gradeLevel": 8, "section": "D", "academicRecord": {"schoolYear": "2023-2024", "quarters": [{"quarter": 1, "subjects": [{"name": "Mathematics", "score": 86}, {"name": "Science", "score": 90}, {"name": "English", "score": 84}, {"name": "Social Studies", "score": 88}, {"name": "Computer Science", "score": 95}]}]}}, {"id": 5, "studentId": 1005, "role": "Student", "fullName": "<PERSON>", "gradeLevel": 9, "section": "A", "academicRecord": {"schoolYear": "2023-2024", "quarters": [{"quarter": 1, "subjects": [{"name": "Physics", "score": 82}, {"name": "Chemistry", "score": 88}, {"name": "English", "score": 91}, {"name": "History", "score": 87}]}]}}, {"id": 6, "studentId": 1006, "role": "Student", "fullName": "<PERSON>", "gradeLevel": 10, "section": "E", "academicRecord": {"schoolYear": "2023-2024", "quarters": [{"quarter": 1, "subjects": [{"name": "Physics", "score": 79}, {"name": "Chemistry", "score": 85}, {"name": "English", "score": 88}, {"name": "Computer Science", "score": 96}]}]}}]
using System.Collections.Generic;
using System.Threading.Tasks;
using api.Models;
using api.DTOs;

namespace api.Services
{
    public interface ITeacherService
    {
        Task<List<Teacher>> GetAllTeachersAsync();
        Task<Teacher?> GetTeacherByIdAsync(int id);
        Task<List<Teacher>> GetTeachersByGradeLevelAsync(int gradeLevel);
        Task<List<Teacher>> GetTeachersBySchoolYearAsync(string schoolYear);
        Task<Teacher> CreateTeacherAsync(Teacher teacher);
        Task<Teacher?> UpdateTeacherAsync(int id, Teacher teacher);
        Task<Teacher?> PatchTeacherAsync(int id, TeacherPatchDto patchDto);
        Task<bool> DeleteTeacherAsync(int id);
    }
} 
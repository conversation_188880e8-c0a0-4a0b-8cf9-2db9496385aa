using Microsoft.AspNetCore.Mvc;
using api.Models;
using api.Services;
using System.Threading.Tasks;
using api.DTOs;

namespace api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TeachersController : ControllerBase
    {
        private readonly ITeacherService _teacherService;
        
        public TeachersController(ITeacherService teacherService)
        {
            _teacherService = teacherService;
        }
        
        [HttpGet]
        public async Task<IActionResult> GetAllTeachers()
        {
            var teachers = await _teacherService.GetAllTeachersAsync();
            return Ok(teachers);
        }
        
        [HttpGet("{id}")]
        public async Task<IActionResult> GetTeacherById(int id)
        {
            var teacher = await _teacherService.GetTeacherByIdAsync(id);
            
            if (teacher == null)
            {
                return NotFound();
            }
            
            return Ok(teacher);
        }
        
        [HttpGet("gradeLevel/{gradeLevel}")]
        public async Task<IActionResult> GetTeachersByGradeLevel(int gradeLevel)
        {
            var teachers = await _teacherService.GetTeachersByGradeLevelAsync(gradeLevel);
            return Ok(teachers);
        }
        
        [HttpGet("schoolYear/{schoolYear}")]
        public async Task<IActionResult> GetTeachersBySchoolYear(string schoolYear)
        {
            var teachers = await _teacherService.GetTeachersBySchoolYearAsync(schoolYear);
            return Ok(teachers);
        }
        
        [HttpPost]
        public async Task<IActionResult> CreateTeacher(Teacher teacher)
        {
            var newTeacher = await _teacherService.CreateTeacherAsync(teacher);
            return CreatedAtAction(nameof(GetTeacherById), new { id = newTeacher.Id }, newTeacher);
        }
        
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTeacher(int id, Teacher teacher)
        {
            var updatedTeacher = await _teacherService.UpdateTeacherAsync(id, teacher);
            
            if (updatedTeacher == null)
            {
                return NotFound();
            }
            
            return Ok(updatedTeacher);
        }
        
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTeacher(int id)
        {
            var result = await _teacherService.DeleteTeacherAsync(id);
            
            if (!result)
            {
                return NotFound();
            }
            
            return NoContent();
        }
        
        [HttpPatch("{id}")]
        public async Task<IActionResult> PatchTeacher(int id, TeacherPatchDto patchDto)
        {
            var updatedTeacher = await _teacherService.PatchTeacherAsync(id, patchDto);
            
            if (updatedTeacher == null)
            {
                return NotFound();
            }
            
            return Ok(updatedTeacher);
        }
    }
} 
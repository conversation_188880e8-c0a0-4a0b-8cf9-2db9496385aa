using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using api.Models;
using api.DTOs;

namespace api.Services
{
    public class StudentService : IStudentService
    {
        private readonly IBlobStorageService _blobStorageService;
        private const string USER_PREFIX = "user/";
        private const string STUDENT_FILE = "student_1.json";
        private readonly string _studentBlobPath;
        
        public StudentService(IBlobStorageService blobStorageService)
        {
            _blobStorageService = blobStorageService;
            _studentBlobPath = $"{USER_PREFIX}{STUDENT_FILE}";
        }
        
        private async Task<List<Student>> GetAllStudentsFromStorageAsync()
        {
            var students = await _blobStorageService.GetBlobDataAsync<List<Student>>(_studentBlobPath);
            return students ?? new List<Student>();
        }
        
        private async Task SaveAllStudentsToStorageAsync(List<Student> students)
        {
            await _blobStorageService.UploadBlobDataAsync(_studentBlob<PERSON><PERSON>, students);
        }
        
        public async Task<List<Student>> GetAllStudentsAsync()
        {
            return await GetAllStudentsFromStorageAsync();
        }
        
        public async Task<Student?> GetStudentByIdAsync(int id)
        {
            var students = await GetAllStudentsFromStorageAsync();
            return students.FirstOrDefault(s => s.Id == id);
        }
        
        public async Task<List<Student>> GetStudentsByGradeLevelAsync(int gradeLevel)
        {
            var students = await GetAllStudentsFromStorageAsync();
            return students.Where(s => s.GradeLevel == gradeLevel).ToList();
        }
        
        public async Task<List<Student>> GetStudentsBySectionAsync(string section)
        {
            var students = await GetAllStudentsFromStorageAsync();
            return students.Where(s => s.Section.Equals(section, StringComparison.OrdinalIgnoreCase)).ToList();
        }
        
        public async Task<Student> CreateStudentAsync(Student student)
        {
            var students = await GetAllStudentsFromStorageAsync();
            
            if (student.Id == 0)
            {
                student.Id = students.Count > 0 ? students.Max(s => s.Id) + 1 : 1;
            }
            else if (students.Any(s => s.Id == student.Id))
            {
                throw new ArgumentException($"Student with Id {student.Id} already exists.");
            }
            
            students.Add(student);
            await SaveAllStudentsToStorageAsync(students);
            
            return student;
        }
        
        public async Task<Student?> UpdateStudentAsync(int id, Student updatedStudent)
        {
            var students = await GetAllStudentsFromStorageAsync();
            var studentIndex = students.FindIndex(s => s.Id == id);
            
            if (studentIndex == -1)
            {
                return null;
            }
            
            updatedStudent.Id = id;
            students[studentIndex] = updatedStudent;
            
            await SaveAllStudentsToStorageAsync(students);
            return updatedStudent;
        }
        
        public async Task<bool> UpdateStudentScoreAsync(int id, string schoolYear, int quarter, string subject, int score)
        {
            var students = await GetAllStudentsFromStorageAsync();
            var student = students.FirstOrDefault(s => s.Id == id);
            
            if (student == null)
            {
                return false;
            }
            
            // Check if school year matches
            if (student.AcademicRecord.SchoolYear != schoolYear)
            {
                return false;
            }
            
            // Find the quarter
            var quarterRecord = student.AcademicRecord.Quarters.FirstOrDefault(q => q.QuarterNumber == quarter);
            if (quarterRecord == null)
            {
                // Create new quarter if it doesn't exist
                quarterRecord = new Quarter { QuarterNumber = quarter };
                student.AcademicRecord.Quarters.Add(quarterRecord);
            }
            
            // Find the subject
            var subjectRecord = quarterRecord.Subjects.FirstOrDefault(s => s.Name.Equals(subject, StringComparison.OrdinalIgnoreCase));
            if (subjectRecord == null)
            {
                // Create new subject if it doesn't exist
                subjectRecord = new Subject { Name = subject };
                quarterRecord.Subjects.Add(subjectRecord);
            }
            
            // Update the score
            subjectRecord.Score = score;
            
            // Save the updated students list
            await SaveAllStudentsToStorageAsync(students);
            return true;
        }
        
        public async Task<bool> DeleteStudentAsync(int id)
        {
            var students = await GetAllStudentsFromStorageAsync();
            var student = students.FirstOrDefault(s => s.Id == id);
            
            if (student == null)
            {
                return false;
            }
            
            students.Remove(student);
            await SaveAllStudentsToStorageAsync(students);
            
            return true;
        }
        
        public async Task<Student?> PatchStudentAsync(int id, StudentPatchDto patchDto)
        {
            var students = await GetAllStudentsFromStorageAsync();
            var student = students.FirstOrDefault(s => s.Id == id);
            
            if (student == null)
            {
                return null;
            }
            
            // Only update the properties that are provided in the patch DTO
            if (patchDto.FullName != null)
            {
                student.FullName = patchDto.FullName;
            }
            
            if (patchDto.GradeLevel.HasValue)
            {
                student.GradeLevel = patchDto.GradeLevel.Value;
            }
            
            if (patchDto.Section != null)
            {
                student.Section = patchDto.Section;
            }
            
            await SaveAllStudentsToStorageAsync(students);
            return student;
        }
    }
} 
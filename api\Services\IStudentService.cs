using System.Collections.Generic;
using System.Threading.Tasks;
using api.Models;
using api.DTOs;

namespace api.Services
{
    public interface IStudentService
    {
        Task<List<Student>> GetAllStudentsAsync();
        Task<Student?> GetStudentByIdAsync(int id);
        Task<List<Student>> GetStudentsByGradeLevelAsync(int gradeLevel);
        Task<List<Student>> GetStudentsBySectionAsync(string section);
        Task<Student> CreateStudentAsync(Student student);
        Task<Student?> UpdateStudentAsync(int id, Student student);
        Task<Student?> PatchStudentAsync(int id, StudentPatchDto patchDto);
        Task<bool> UpdateStudentScoreAsync(int id, string schoolYear, int quarter, string subject, int score);
        Task<bool> DeleteStudentAsync(int id);
    }
} 
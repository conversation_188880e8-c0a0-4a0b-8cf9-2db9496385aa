# K-12 Grading System API

A RESTful API built with .NET 8 and ASP.NET Core for a K to 12 Grading System. The API supports operations for Teachers and Students with data stored in Azure Blob Storage.

## Architecture

The API follows clean architecture with:

- Models
- DTOs
- Controllers
- Services

## API Endpoints

### Teachers

- `GET /api/teachers` - Get all teachers
- `GET /api/teachers/{id}` - Get teacher by ID
- `GET /api/teachers/gradeLevel/{gradeLevel}` - Get teachers by grade level
- `GET /api/teachers/schoolYear/{schoolYear}` - Get teachers by school year
- `POST /api/teachers` - Create a new teacher
- `PUT /api/teachers/{id}` - Update a teacher
- `DELETE /api/teachers/{id}` - Delete a teacher

### Students

- `GET /api/students` - Get all students
- `GET /api/students/{id}` - Get student by ID
- `GET /api/students/gradeLevel/{gradeLevel}` - Get students by grade level
- `GET /api/students/section/{section}` - Get students by section
- `POST /api/students` - Create a new student
- `PUT /api/students/{id}` - Update a student
- `PUT /api/students/{id}/score` - Update a student's subject score
- `DELETE /api/students/{id}` - Delete a student

### Data Management

- `POST /api/data/upload-sample` - Upload sample data to blob storage

## Data Models

### Teacher

```json
{
  "id": 1,
  "teacherId": 0,
  "role": "string",
  "fullName": "string",
  "gradeLevelsHandled": [5, 6],
  "sections": ["A", "B"],
  "subjects": ["string"],
  "schoolYear": "string"
}
```

### Student

```json
{
  "id": 1,
  "studentId": 0,
  "role": "string",
  "fullName": "string",
  "gradeLevel": 0,
  "section": "string",
  "academicRecord": {
    "schoolYear": "string",
    "quarters": [
      {
        "quarter": 0,
        "subjects": [
          {
            "name": "string",
            "score": 0
          }
        ]
      }
    ]
  }
}
```

## Storage Structure

Data is stored in Azure Blob Storage with the following structure:

- Container: `grading`
- Structure:
  - `user/teacher_1.json` - Contains all teacher records in a single JSON array
  - `user/student_1.json` - Contains all student records in a single JSON array

## Running the API

1. Ensure Azure Blob Storage credentials are configured in `appsettings.json`
2. Run the API with `dotnet run`
3. Access Swagger UI at `https://localhost:5001/swagger` (or the configured port)
4. Use the `/api/data/upload-sample` endpoint to upload sample data to blob storage

## Sample Data

Sample data files have been provided:

- `SampleTeachers.json` - Contains sample teacher records
- `SampleStudents.json` - Contains sample student records

using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DataController : ControllerBase
    {
        //testing
        private readonly IConfiguration _configuration;
        
        public DataController(IConfiguration configuration)
        {
            _configuration = configuration;
        }
        
        [HttpPost("upload-sample")]
        public async Task<IActionResult> UploadSampleData()
        {
            try
            {
                var uploader = new UploadSampleData(_configuration);
                await uploader.UploadJsonFilesAsync();
                return Ok(new { message = "Sample data uploaded successfully" });
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, new { error = $"Failed to upload sample data: {ex.Message}" });
            }
        }
    }
} 
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\SampleStudents.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\SampleTeachers.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\api.staticwebassets.endpoints.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\api.exe
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\api.deps.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\api.runtimeconfig.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\api.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\api.pdb
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Azure.Storage.Blobs.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Azure.Storage.Common.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\System.ClientModel.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\System.IO.Hashing.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.csproj.AssemblyReference.cache
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.AssemblyInfoInputs.cache
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.AssemblyInfo.cs
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.csproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.sourcelink.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\scopedcss\bundle\api.styles.css
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.csproj.Up2Date
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\refint\api.dll
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.pdb
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\api.genruntimeconfig.cache
C:\Users\<USER>\OneDrive\Desktop\Odel_GradingSystem\api\obj\Debug\net8.0\ref\api.dll

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Configuration;

namespace api.Services
{
    public class BlobStorageService : IBlobStorageService
    {
        private readonly BlobContainerClient _containerClient;
        
        public BlobStorageService(IConfiguration configuration)
        {
            var connectionString = configuration["BlobStorage:ConnectionString"];
            var containerName = configuration["BlobStorage:ContainerName"];
            
            if (string.IsNullOrEmpty(connectionString) || string.IsNullOrEmpty(containerName))
            {
                throw new ArgumentException("Missing Blob Storage configuration");
            }
            
            var blobServiceClient = new BlobServiceClient(connectionString);
            _containerClient = blobServiceClient.GetBlobContainerClient(containerName);
            _containerClient.CreateIfNotExists();
        }
        
        public async Task<T?> GetBlobDataAsync<T>(string blobName) where T : class
        {
            var blobClient = _containerClient.GetBlobClient(blobName);
            
            if (!await blobClient.ExistsAsync())
            {
                return null;
            }
            
            var response = await blobClient.DownloadAsync();
            using var streamReader = new StreamReader(response.Value.Content);
            var content = await streamReader.ReadToEndAsync();
            
            return JsonSerializer.Deserialize<T>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        
        public async Task UploadBlobDataAsync<T>(string blobName, T data) where T : class
        {
            var blobClient = _containerClient.GetBlobClient(blobName);
            var content = JsonSerializer.Serialize(data);
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(content));
            
            await blobClient.UploadAsync(stream, overwrite: true);
        }
        
        public async Task DeleteBlobAsync(string blobName)
        {
            var blobClient = _containerClient.GetBlobClient(blobName);
            await blobClient.DeleteIfExistsAsync();
        }
        
        public async Task<bool> BlobExistsAsync(string blobName)
        {
            var blobClient = _containerClient.GetBlobClient(blobName);
            return await blobClient.ExistsAsync();
        }
        
        public async Task<List<string>> ListBlobsAsync(string prefix)
        {
            var blobItems = new List<string>();
            var resultSegment = _containerClient.GetBlobsAsync(prefix: prefix)
                .AsPages(default, 100);

            await foreach (var blobPage in resultSegment)
            {
                foreach (var blobItem in blobPage.Values)
                {
                    blobItems.Add(blobItem.Name);
                }
            }
            
            return blobItems;
        }
    }
} 
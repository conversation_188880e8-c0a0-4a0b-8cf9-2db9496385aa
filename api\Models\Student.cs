using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace api.Models
{
    public class Student
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }
        
        [JsonPropertyName("studentId")]
        public int StudentId { get; set; }
        
        [JsonPropertyName("role")]
        public string Role { get; set; } = string.Empty;
        
        [JsonPropertyName("fullName")]
        public string FullName { get; set; } = string.Empty;
        
        [JsonPropertyName("gradeLevel")]
        public int GradeLevel { get; set; }
        
        [JsonPropertyName("section")]
        public string Section { get; set; } = string.Empty;
        
        [JsonPropertyName("academicRecord")]
        public AcademicRecord AcademicRecord { get; set; } = new AcademicRecord();
    }

    public class AcademicRecord
    {
        [JsonPropertyName("schoolYear")]
        public string SchoolYear { get; set; } = string.Empty;
        
        [JsonPropertyName("quarters")]
        public List<Quarter> Quarters { get; set; } = new List<Quarter>();
    }

    public class Quarter
    {
        [JsonPropertyName("quarter")]
        public int QuarterNumber { get; set; }
        
        [JsonPropertyName("subjects")]
        public List<Subject> Subjects { get; set; } = new List<Subject>();
    }

    public class Subject
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;
        
        [JsonPropertyName("score")]
        public int Score { get; set; }
    }
} 
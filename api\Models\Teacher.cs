using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace api.Models
{
    public class Teacher
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }
        
        [JsonPropertyName("teacherId")]
        public int TeacherId { get; set; }
        
        [JsonPropertyName("role")]
        public string Role { get; set; } = string.Empty;
        
        [JsonPropertyName("fullName")]
        public string FullName { get; set; } = string.Empty;
        
        [JsonPropertyName("gradeLevelsHandled")]
        public List<int> GradeLevelsHandled { get; set; } = new List<int>();
        
        [JsonPropertyName("sections")]
        public List<string> Sections { get; set; } = new List<string>();
        
        [JsonPropertyName("subjects")]
        public List<string> Subjects { get; set; } = new List<string>();
        
        [JsonPropertyName("schoolYear")]
        public string SchoolYear { get; set; } = string.Empty;
    }
} 
# ASP.NET Core
# Build and test ASP.NET Core projects targeting .NET Core.

trigger:
- main

pool:
  vmImage: ubuntu-latest

variables:
  buildConfiguration: 'Release'
  projectPath: '**/*.csproj'
  testProjectPath: '**/*Tests.csproj'

stages:
- stage: Build
  displayName: Build and Test
  jobs:
  - job: BuildJob
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '8.0.x'

    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet packages'
      inputs:
        command: 'restore'
        projects: '$(projectPath)'
        feedsToUse: 'select'

    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: '$(projectPath)'
        arguments: '--configuration $(buildConfiguration) --no-restore'
    
    - task: DotNetCoreCLI@2
      displayName: 'Run unit tests'
      inputs:
        command: 'test'
        projects: '$(testProjectPath)'
        arguments: '--configuration $(buildConfiguration) --no-build'
        publishTestResults: true
    
    - task: DotNetCoreCLI@2
      displayName: 'Publish web app'
      inputs:
        command: 'publish'
        projects: '$(projectPath)'
        arguments: '--configuration $(buildConfiguration) --no-build --output $(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true
    
    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)'
        artifactName: 'drop'
        publishLocation: 'Container'

- stage: Deploy
  displayName: Deploy to Dev
  dependsOn: Build
  condition: succeeded()
  jobs:
  - job: DeployJob
    steps:
    - task: DownloadBuildArtifacts@1
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: 'drop'
        downloadPath: '$(System.ArtifactsDirectory)'
    
    - task: AzureWebApp@1
      displayName: 'Deploy to Azure Web App'
      inputs:
        azureSubscription: 'Azure for Students(4d379be0-5e7a-4feb-8784-d3f20654bbbc)'
        appType: 'webApp'
        appName: 'GradingSystemAPI'
        package: '$(System.ArtifactsDirectory)/drop/*.zip'
        deploymentMethod: 'auto'
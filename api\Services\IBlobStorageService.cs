using System.Threading.Tasks;

namespace api.Services
{
    public interface IBlobStorageService
    {
        Task<T?> GetBlobDataAsync<T>(string blobName) where T : class;
        Task UploadBlobDataAsync<T>(string blobName, T data) where T : class;
        Task DeleteBlobAsync(string blobName);
        Task<bool> BlobExistsAsync(string blobName);
        Task<List<string>> ListBlobsAsync(string prefix);
    }
} 
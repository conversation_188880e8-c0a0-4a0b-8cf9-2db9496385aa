using Microsoft.AspNetCore.Mvc;
using api.DTOs;
using api.Models;
using api.Services;
using System.Threading.Tasks;

namespace api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class StudentsController : ControllerBase
    {
        private readonly IStudentService _studentService;
        
        public StudentsController(IStudentService studentService)
        {
            _studentService = studentService;
        }
        
        [HttpGet]
        public async Task<IActionResult> GetAllStudents()
        {
            var students = await _studentService.GetAllStudentsAsync();
            return Ok(students);
        }
        
        [HttpGet("{id}")]
        public async Task<IActionResult> GetStudentById(int id)
        {
            var student = await _studentService.GetStudentByIdAsync(id);
            
            if (student == null)
            {
                return NotFound();
            }
            
            return Ok(student);
        }
        
        [HttpGet("gradeLevel/{gradeLevel}")]
        public async Task<IActionResult> GetStudentsByGradeLevel(int gradeLevel)
        {
            var students = await _studentService.GetStudentsByGradeLevelAsync(gradeLevel);
            return Ok(students);
        }
        
        [HttpGet("section/{section}")]
        public async Task<IActionResult> GetStudentsBySection(string section)
        {
            var students = await _studentService.GetStudentsBySectionAsync(section);
            return Ok(students);
        }
        
        [HttpPost]
        public async Task<IActionResult> CreateStudent(Student student)
        {
            var newStudent = await _studentService.CreateStudentAsync(student);
            return CreatedAtAction(nameof(GetStudentById), new { id = newStudent.Id }, newStudent);
        }
        
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateStudent(int id, Student student)
        {
            var updatedStudent = await _studentService.UpdateStudentAsync(id, student);
            
            if (updatedStudent == null)
            {
                return NotFound();
            }
            
            return Ok(updatedStudent);
        }
        
        [HttpPut("{id}/score")]
        public async Task<IActionResult> UpdateStudentScore(int id, UpdateStudentScoreRequest request)
        {
            var result = await _studentService.UpdateStudentScoreAsync(
                id,
                request.SchoolYear,
                request.Quarter,
                request.Subject,
                request.Score);
            
            if (!result)
            {
                return NotFound();
            }
            
            return NoContent();
        }
        
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteStudent(int id)
        {
            var result = await _studentService.DeleteStudentAsync(id);
            
            if (!result)
            {
                return NotFound();
            }
            
            return NoContent();
        }
        
        [HttpPatch("{id}")]
        public async Task<IActionResult> PatchStudent(int id, StudentPatchDto patchDto)
        {
            var updatedStudent = await _studentService.PatchStudentAsync(id, patchDto);
            
            if (updatedStudent == null)
            {
                return NotFound();
            }
            
            return Ok(updatedStudent);
        }
    }
} 
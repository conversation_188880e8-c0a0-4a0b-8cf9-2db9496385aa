using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Azure.Storage.Blobs;

namespace api
{
    public class UploadSampleData
    {
        private readonly string _connectionString;
        private readonly string _containerName;
        private const string USER_PREFIX = "user/";
        private const string TEACHER_FILE = "teacher_1.json";
        private const string STUDENT_FILE = "student_1.json";
        
        public UploadSampleData(IConfiguration configuration)
        {
            _connectionString = configuration["BlobStorage:ConnectionString"];
            _containerName = configuration["BlobStorage:ContainerName"];
            
            if (string.IsNullOrEmpty(_connectionString) || string.IsNullOrEmpty(_containerName))
            {
                throw new ArgumentException("Missing Blob Storage configuration");
            }
        }
        
        public async Task UploadJsonFilesAsync()
        {
            Console.WriteLine("Starting upload of sample data to Azure Blob Storage...");
            
            var blobServiceClient = new BlobServiceClient(_connectionString);
            var containerClient = blobServiceClient.GetBlobContainerClient(_containerName);
            
            // Create container if it doesn't exist
            await containerClient.CreateIfNotExistsAsync();
            
            // Upload teacher data
            await UploadTeacherDataAsync(containerClient);
            
            // Upload student data
            await UploadStudentDataAsync(containerClient);
            
            Console.WriteLine("Sample data upload completed!");
        }
        
        private async Task UploadTeacherDataAsync(BlobContainerClient containerClient)
        {
            try
            {
                var teacherJson = File.ReadAllText("SampleTeachers.json");
                var blobClient = containerClient.GetBlobClient($"{USER_PREFIX}{TEACHER_FILE}");
                
                using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(teacherJson));
                await blobClient.UploadAsync(stream, overwrite: true);
                
                Console.WriteLine($"Teacher data uploaded to {USER_PREFIX}{TEACHER_FILE}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading teacher data: {ex.Message}");
            }
        }
        
        private async Task UploadStudentDataAsync(BlobContainerClient containerClient)
        {
            try
            {
                var studentJson = File.ReadAllText("SampleStudents.json");
                var blobClient = containerClient.GetBlobClient($"{USER_PREFIX}{STUDENT_FILE}");
                
                using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(studentJson));
                await blobClient.UploadAsync(stream, overwrite: true);
                
                Console.WriteLine($"Student data uploaded to {USER_PREFIX}{STUDENT_FILE}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading student data: {ex.Message}");
            }
        }
        
        // Main method for running from command line
        public static async Task UploadData(string[] args)
        {
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .AddEnvironmentVariables()
                .Build();
            
            var uploader = new UploadSampleData(configuration);
            await uploader.UploadJsonFilesAsync();
        }
    }
} 
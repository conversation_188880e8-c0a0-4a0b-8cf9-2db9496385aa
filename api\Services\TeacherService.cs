using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using api.Models;
using api.DTOs;

namespace api.Services
{
    public class TeacherService : ITeacherService
    {
        private readonly IBlobStorageService _blobStorageService;
        private const string USER_PREFIX = "user/";
        private const string TEACHER_FILE = "teacher_1.json";
        private readonly string _teacherBlobPath;
        
        public TeacherService(IBlobStorageService blobStorageService)
        {
            _blobStorageService = blobStorageService;
            _teacherBlobPath = $"{USER_PREFIX}{TEACHER_FILE}";
        }
        
        private async Task<List<Teacher>> GetAllTeachersFromStorageAsync()
        {
            var teachers = await _blobStorageService.GetBlobDataAsync<List<Teacher>>(_teacherBlobPath);
            return teachers ?? new List<Teacher>();
        }
        
        private async Task SaveAllTeachersToStorageAsync(List<Teacher> teachers)
        {
            await _blobStorageService.UploadBlobDataAsync(_teacherBlobPath, teachers);
        }
        
        public async Task<List<Teacher>> GetAllTeachersAsync()
        {
            return await GetAllTeachersFromStorageAsync();
        }
        
        public async Task<Teacher?> GetTeacherByIdAsync(int id)
        {
            var teachers = await GetAllTeachersFromStorageAsync();
            return teachers.FirstOrDefault(t => t.Id == id);
        }
        
        public async Task<List<Teacher>> GetTeachersByGradeLevelAsync(int gradeLevel)
        {
            var teachers = await GetAllTeachersFromStorageAsync();
            return teachers.Where(t => t.GradeLevelsHandled.Contains(gradeLevel)).ToList();
        }
        
        public async Task<List<Teacher>> GetTeachersBySchoolYearAsync(string schoolYear)
        {
            var teachers = await GetAllTeachersFromStorageAsync();
            return teachers.Where(t => t.SchoolYear == schoolYear).ToList();
        }
        
        public async Task<Teacher> CreateTeacherAsync(Teacher teacher)
        {
            var teachers = await GetAllTeachersFromStorageAsync();
            
            if (teacher.Id == 0)
            {
                teacher.Id = teachers.Count > 0 ? teachers.Max(t => t.Id) + 1 : 1;
            }
            else if (teachers.Any(t => t.Id == teacher.Id))
            {
                throw new ArgumentException($"Teacher with Id {teacher.Id} already exists.");
            }
            
            teachers.Add(teacher);
            await SaveAllTeachersToStorageAsync(teachers);
            
            return teacher;
        }
        
        public async Task<Teacher?> UpdateTeacherAsync(int id, Teacher updatedTeacher)
        {
            var teachers = await GetAllTeachersFromStorageAsync();
            var teacherIndex = teachers.FindIndex(t => t.Id == id);
            
            if (teacherIndex == -1)
            {
                return null;
            }
            
            updatedTeacher.Id = id;
            teachers[teacherIndex] = updatedTeacher;
            
            await SaveAllTeachersToStorageAsync(teachers);
            return updatedTeacher;
        }
        
        public async Task<bool> DeleteTeacherAsync(int id)
        {
            var teachers = await GetAllTeachersFromStorageAsync();
            var teacher = teachers.FirstOrDefault(t => t.Id == id);
            
            if (teacher == null)
            {
                return false;
            }
            
            teachers.Remove(teacher);
            await SaveAllTeachersToStorageAsync(teachers);
            
            return true;
        }
        
        public async Task<Teacher?> PatchTeacherAsync(int id, TeacherPatchDto patchDto)
        {
            var teachers = await GetAllTeachersFromStorageAsync();
            var teacher = teachers.FirstOrDefault(t => t.Id == id);
            
            if (teacher == null)
            {
                return null;
            }
            
            // Only update the properties that are provided in the patch DTO
            if (patchDto.FullName != null)
            {
                teacher.FullName = patchDto.FullName;
            }
            
            if (patchDto.GradeLevelsHandled != null)
            {
                teacher.GradeLevelsHandled = patchDto.GradeLevelsHandled;
            }
            
            if (patchDto.Sections != null)
            {
                teacher.Sections = patchDto.Sections;
            }
            
            if (patchDto.Subjects != null)
            {
                teacher.Subjects = patchDto.Subjects;
            }
            
            if (patchDto.SchoolYear != null)
            {
                teacher.SchoolYear = patchDto.SchoolYear;
            }
            
            await SaveAllTeachersToStorageAsync(teachers);
            return teacher;
        }
    }
} 
using System.ComponentModel.DataAnnotations;

namespace api.DTOs
{
    public class UpdateStudentScoreRequest
    {
        [Required]
        public string SchoolYear { get; set; } = string.Empty;
        
        [Required]
        [Range(1, 4)]
        public int Quarter { get; set; }
        
        [Required]
        public string Subject { get; set; } = string.Empty;
        
        [Required]
        [Range(0, 100)]
        public int Score { get; set; }
    }
} 